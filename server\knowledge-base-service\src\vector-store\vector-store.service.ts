import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChromaClient, Collection, OpenAIEmbeddingFunction } from 'chromadb';
import { EmbeddingsService } from '../embeddings/embeddings.service';
import { TextChunk } from '../documents/processors/document-processor.service';

export interface SearchOptions {
  topK?: number;
  threshold?: number;
  filter?: Record<string, any>;
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata: Record<string, any>;
}

@Injectable()
export class VectorStoreService {
  private chromaClient: ChromaClient;
  private collections: Map<string, Collection> = new Map();

  constructor(
    private configService: ConfigService,
    private embeddingsService: EmbeddingsService,
  ) {
    this.initializeChromaClient();
  }

  /**
   * 初始化Chroma客户端
   */
  private initializeChromaClient(): void {
    const chromaUrl = this.configService.get<string>('CHROMA_URL', 'http://localhost:8000');
    
    this.chromaClient = new ChromaClient({
      path: chromaUrl,
    });
  }

  /**
   * 创建集合
   */
  async createCollection(knowledgeBaseId: string): Promise<void> {
    try {
      const collectionName = `kb_${knowledgeBaseId}`;
      
      const collection = await this.chromaClient.createCollection({
        name: collectionName,
        metadata: {
          description: `Knowledge base ${knowledgeBaseId}`,
          created_at: new Date().toISOString(),
        },
      });

      this.collections.set(knowledgeBaseId, collection);
    } catch (error) {
      if (error.message?.includes('already exists')) {
        // 集合已存在，获取现有集合
        await this.getCollection(knowledgeBaseId);
      } else {
        throw new BadRequestException(`创建向量集合失败: ${error.message}`);
      }
    }
  }

  /**
   * 获取集合
   */
  async getCollection(knowledgeBaseId: string): Promise<Collection> {
    if (this.collections.has(knowledgeBaseId)) {
      return this.collections.get(knowledgeBaseId);
    }

    try {
      const collectionName = `kb_${knowledgeBaseId}`;
      const collection = await this.chromaClient.getCollection({
        name: collectionName,
      });

      this.collections.set(knowledgeBaseId, collection);
      return collection;
    } catch (error) {
      throw new BadRequestException(`获取向量集合失败: ${error.message}`);
    }
  }

  /**
   * 删除集合
   */
  async deleteCollection(knowledgeBaseId: string): Promise<void> {
    try {
      const collectionName = `kb_${knowledgeBaseId}`;
      await this.chromaClient.deleteCollection({ name: collectionName });
      this.collections.delete(knowledgeBaseId);
    } catch (error) {
      // 忽略集合不存在的错误
      if (!error.message?.includes('does not exist')) {
        throw new BadRequestException(`删除向量集合失败: ${error.message}`);
      }
    }
  }

  /**
   * 添加文档向量
   */
  async addDocument(
    knowledgeBaseId: string,
    documentId: string,
    chunks: TextChunk[],
  ): Promise<void> {
    try {
      const collection = await this.getCollection(knowledgeBaseId);
      
      // 批量生成嵌入
      const texts = chunks.map(chunk => chunk.text);
      const embeddings = await this.embeddingsService.generateBatchEmbeddings(texts);

      // 准备数据
      const ids = chunks.map(chunk => `${documentId}_${chunk.id}`);
      const metadatas = chunks.map(chunk => ({
        documentId,
        chunkId: chunk.id,
        startIndex: chunk.startIndex,
        endIndex: chunk.endIndex,
        ...chunk.metadata,
      }));

      // 添加到向量数据库
      await collection.add({
        ids,
        embeddings,
        documents: texts,
        metadatas,
      });
    } catch (error) {
      throw new BadRequestException(`添加文档向量失败: ${error.message}`);
    }
  }

  /**
   * 删除文档向量
   */
  async deleteDocument(knowledgeBaseId: string, documentId: string): Promise<void> {
    try {
      const collection = await this.getCollection(knowledgeBaseId);
      
      // 查询文档的所有向量ID
      const results = await collection.get({
        where: { documentId },
      });

      if (results.ids.length > 0) {
        await collection.delete({
          ids: results.ids,
        });
      }
    } catch (error) {
      throw new BadRequestException(`删除文档向量失败: ${error.message}`);
    }
  }

  /**
   * 搜索向量
   */
  async search(
    knowledgeBaseId: string,
    query: string,
    options: SearchOptions = {},
  ): Promise<SearchResult[]> {
    try {
      const collection = await this.getCollection(knowledgeBaseId);
      
      // 生成查询向量
      const queryEmbedding = await this.embeddingsService.generateEmbedding(query);
      
      // 执行搜索
      const results = await collection.query({
        queryEmbeddings: [queryEmbedding],
        nResults: options.topK || 5,
        where: options.filter,
      });

      // 处理结果
      const searchResults: SearchResult[] = [];
      
      if (results.documents && results.documents[0]) {
        for (let i = 0; i < results.documents[0].length; i++) {
          const score = 1 - (results.distances?.[0]?.[i] || 0); // 转换为相似度分数
          
          // 应用阈值过滤
          if (options.threshold && score < options.threshold) {
            continue;
          }

          searchResults.push({
            id: results.ids[0][i],
            content: results.documents[0][i],
            score,
            metadata: results.metadatas?.[0]?.[i] || {},
          });
        }
      }

      return searchResults;
    } catch (error) {
      throw new BadRequestException(`向量搜索失败: ${error.message}`);
    }
  }

  /**
   * 获取向量数量
   */
  async getVectorCount(knowledgeBaseId: string): Promise<number> {
    try {
      const collection = await this.getCollection(knowledgeBaseId);
      const count = await collection.count();
      return count;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取集合信息
   */
  async getCollectionInfo(knowledgeBaseId: string): Promise<any> {
    try {
      const collection = await this.getCollection(knowledgeBaseId);
      const count = await collection.count();
      
      return {
        name: `kb_${knowledgeBaseId}`,
        count,
        metadata: collection.metadata,
      };
    } catch (error) {
      throw new BadRequestException(`获取集合信息失败: ${error.message}`);
    }
  }

  /**
   * 更新文档向量
   */
  async updateDocument(
    knowledgeBaseId: string,
    documentId: string,
    chunks: TextChunk[],
  ): Promise<void> {
    // 先删除旧的向量
    await this.deleteDocument(knowledgeBaseId, documentId);
    
    // 再添加新的向量
    await this.addDocument(knowledgeBaseId, documentId, chunks);
  }

  /**
   * 批量删除向量
   */
  async batchDelete(knowledgeBaseId: string, documentIds: string[]): Promise<void> {
    try {
      const collection = await this.getCollection(knowledgeBaseId);
      
      for (const documentId of documentIds) {
        const results = await collection.get({
          where: { documentId },
        });

        if (results.ids.length > 0) {
          await collection.delete({
            ids: results.ids,
          });
        }
      }
    } catch (error) {
      throw new BadRequestException(`批量删除向量失败: ${error.message}`);
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.chromaClient.heartbeat();
      return true;
    } catch (error) {
      return false;
    }
  }
}
